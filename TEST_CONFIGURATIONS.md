# Robot World Test Configurations

This document explains how to run the acceptance tests with different world configurations.

## Test Organization

The tests are organized by world configuration to ensure proper isolation and deterministic behavior:

### 1. **LaunchRobotEmptyWorldTest** - Empty Worlds (No Obstacles)
- **1x1 Empty World**: Basic launch tests, capacity limits (1 robot max)
- **2x2 Empty World**: Multiple robot launches, full world scenarios (9 robots max)

### 2. **LaunchRobotObstacleWorldTest** - Worlds with Obstacles  
- **1x1 World with Obstacle**: Should have 0 capacity (obstacle blocks only position)
- **2x2 World with Obstacle**: Should have 8 capacity (9 positions - 1 obstacle)

### 3. **LookRobotEmptyWorldTest** - Look Command Tests
- **Empty World**: Look command functionality and robot detection

## How to Run Tests

### Step 1: Build the Project
```bash
mvn clean package -DskipTests
```

### Step 2: Start Server with Appropriate Configuration

#### For LaunchRobotEmptyWorldTest:

**1x1 Empty World Tests (Orders 1-3):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1
```

**2x2 Empty World Tests (Orders 4-6):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2
```

#### For LaunchRobotObstacleWorldTest:

**1x1 World with Obstacle at (0,0) (Order 1):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0
```

**2x2 World with Obstacle at (1,1) (Orders 2-3):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 1,1
```

**2x2 World with Obstacle at (0,0) (Order 4):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 0,0
```

**2x2 World with Multiple Obstacles (Order 5):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 0,0 -o 1,1
```

#### For LookRobotEmptyWorldTest:

**Empty World (Default):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar
```

### Step 3: Run Tests

#### Run Individual Test Classes:
```bash
# Empty world tests
mvn test -Dtest=LaunchRobotEmptyWorldTest

# Obstacle world tests  
mvn test -Dtest=LaunchRobotObstacleWorldTest

# Look command tests
mvn test -Dtest=LookRobotEmptyWorldTest
```

#### Run Specific Test Methods:
```bash
# Run only 1x1 empty world tests
mvn test -Dtest=LaunchRobotEmptyWorldTest#launch1x1EmptyWorldSuccess
mvn test -Dtest=LaunchRobotEmptyWorldTest#launch1x1EmptyWorldFull
mvn test -Dtest=LaunchRobotEmptyWorldTest#launch1x1EmptyWorldDuplicateName

# Run only 2x2 empty world tests
mvn test -Dtest=LaunchRobotEmptyWorldTest#launch2x2EmptyWorldMultipleRobots
mvn test -Dtest=LaunchRobotEmptyWorldTest#launch2x2EmptyWorldFull
```

## Expected Test Results

### LaunchRobotEmptyWorldTest
- **1x1 Empty World**: 1 robot capacity, second robot fails
- **2x2 Empty World**: 9 robot capacity, 10th robot fails

### LaunchRobotObstacleWorldTest  
- **1x1 with Obstacle**: 0 robot capacity (all positions blocked)
- **2x2 with 1 Obstacle**: 8 robot capacity (9 - 1 obstacle)
- **2x2 with 2 Obstacles**: 7 robot capacity (9 - 2 obstacles)

### LookRobotEmptyWorldTest
- **Empty World**: Look returns empty objects/robots arrays
- **With Other Robots**: Look detects nearby robots within visibility range

## World Coordinate Systems

### 1x1 World:
- **Positions**: (0,0)
- **Capacity**: 1 robot

### 2x2 World:
- **Positions**: (-1,-1), (-1,0), (-1,1), (0,-1), (0,0), (0,1), (1,-1), (1,0), (1,1)
- **Capacity**: 9 robots (without obstacles)

## Test Isolation

Each test method is isolated through:
- **@BeforeEach**: Connects to server
- **@AfterEach**: Disconnects from server (triggers robot cleanup)
- **Server Reset**: Robots are automatically cleared when clients disconnect

## Troubleshooting

### Port Already in Use
If you get "port already in use" errors:
```bash
# Find process using port 5000
netstat -ano | findstr :5000

# Kill the process (Windows)
taskkill /PID <PID> /F

# Kill the process (Linux/Mac)
kill -9 <PID>
```

### Tests Failing Due to Wrong World Configuration
Make sure you start the server with the correct configuration for the test you're running. Each test class expects specific world configurations as documented above.

### Server Not Responding
- Ensure the server is fully started before running tests
- Check server logs for any startup errors
- Verify the correct JAR file is being used

## Command Line Arguments Reference

- `-s <size>`: Set world size (1 for 1x1, 2 for 2x2, etc.)
- `-o <x,y>`: Add obstacle at position (x,y) - can be used multiple times
- `-p <port>`: Set server port (default: 5000)

Example:
```bash
java -jar server.jar -s 2 -o 1,1 -o 0,0 -p 5001
```
This creates a 2x2 world with obstacles at (1,1) and (0,0) on port 5001.
