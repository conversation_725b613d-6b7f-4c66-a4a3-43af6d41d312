package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for launching robots in worlds with obstacles
 * 
 * Test Organization:
 * - 1x1 World with Obstacle: Should have 0 capacity (obstacle blocks only position)
 * - 2x2 World with Obstacle: Should have 8 capacity (9 positions - 1 obstacle)
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class LaunchRobotObstacleWorldTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    // ========== 1x1 WORLD WITH OBSTACLE TESTS ==========
    
    @Test
    @Order(1)
    @DisplayName("1x1 World with obstacle at (0,0): No robots should be able to launch")
    void launch1x1WorldWithObstacleAtOrigin() {
        // Given: Connected to server with 1x1 world with obstacle at (0,0)
        // Server should be started with: java -jar server.jar -s 1 -o 0,0
        assertTrue(serverClient.isConnected());

        // When: Try to launch a robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Should fail because only position is blocked by obstacle
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world", 
                    response.get("data").get("message").asText());
    }

    // ========== 2x2 WORLD WITH OBSTACLE TESTS ==========

    @Test
    @Order(2)
    @DisplayName("2x2 World with obstacle at (1,1): Robots should not spawn at obstacle position")
    void launch2x2WorldWithObstacleRobotsAvoidObstacle() {
        // Given: Connected to server with 2x2 world with obstacle at (1,1)
        // Server should be started with: java -jar server.jar -s 2 -o 1,1
        assertTrue(serverClient.isConnected());

        // When: Launch 8 robots (all available positions except obstacle)
        for (int i = 0; i < 8; i++) {
            String request = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            
            // Then: Each robot should launch successfully
            assertEquals("OK", response.get("result").asText(), 
                        "Robot" + i + " should launch successfully");
            
            // And: No robot should be at obstacle position [1,1]
            JsonNode position = response.get("data").get("position");
            assertFalse(position.get(0).asInt() == 1 && position.get(1).asInt() == 1,
                       "Robot" + i + " should not be placed at obstacle position [1,1]");
        }
    }

    @Test
    @Order(3)
    @DisplayName("2x2 World with obstacle at (1,1): Should be full after 8 robots")
    void launch2x2WorldWithObstacleFull() {
        // Given: Connected to server with 2x2 world with obstacle at (1,1)
        // Available positions: 9 total - 1 obstacle = 8 positions
        assertTrue(serverClient.isConnected());

        // And: 8 robots successfully launched (filling all available positions)
        for (int i = 0; i < 8; i++) {
            String request = "{" +
                    "\"robot\": \"Terminator" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            assertEquals("OK", response.get("result").asText(),
                        "Terminator" + i + " should launch successfully");
        }

        // When: Try to launch 9th robot
        String extraRequest = "{" +
                "\"robot\": \"T1000\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world", 
                    response.get("data").get("message").asText());
    }

    @Test
    @Order(4)
    @DisplayName("2x2 World with obstacle at (0,0): First robot should not spawn at origin")
    void launch2x2WorldWithObstacleAtOrigin() {
        // Given: Connected to server with 2x2 world with obstacle at (0,0)
        // Server should be started with: java -jar server.jar -s 2 -o 0,0
        assertTrue(serverClient.isConnected());

        // When: Launch first robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Should succeed but not at (0,0)
        assertEquals("OK", response.get("result").asText());
        JsonNode position = response.get("data").get("position");
        assertFalse(position.get(0).asInt() == 0 && position.get(1).asInt() == 0,
                   "Robot should not be placed at obstacle position [0,0]");
    }

    @Test
    @Order(5)
    @DisplayName("2x2 World with multiple obstacles: Should respect all obstacle positions")
    void launch2x2WorldWithMultipleObstacles() {
        // Given: Connected to server with 2x2 world with obstacles at (0,0) and (1,1)
        // Server should be started with: java -jar server.jar -s 2 -o 0,0 -o 1,1
        // Available positions: 9 total - 2 obstacles = 7 positions
        assertTrue(serverClient.isConnected());

        // When: Launch 7 robots (all available positions)
        for (int i = 0; i < 7; i++) {
            String request = "{" +
                    "\"robot\": \"RoboCop" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            
            // Then: Each robot should launch successfully
            assertEquals("OK", response.get("result").asText(),
                        "RoboCop" + i + " should launch successfully");
            
            // And: No robot should be at obstacle positions
            JsonNode position = response.get("data").get("position");
            int x = position.get(0).asInt();
            int y = position.get(1).asInt();
            assertFalse((x == 0 && y == 0) || (x == 1 && y == 1),
                       "Robot" + i + " should not be at obstacle positions [0,0] or [1,1]");
        }

        // When: Try to launch 8th robot
        String extraRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world", 
                    response.get("data").get("message").asText());
    }

    // ========== EDGE CASE TESTS ==========

    @Test
    @Order(6)
    @DisplayName("2x2 World with obstacle: Robots should get valid positions within world bounds")
    void launch2x2WorldWithObstacleValidPositions() {
        // Given: Connected to server with 2x2 world with obstacle
        assertTrue(serverClient.isConnected());

        // When: Launch a robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Should succeed with valid position
        assertEquals("OK", response.get("result").asText());
        JsonNode position = response.get("data").get("position");
        int x = position.get(0).asInt();
        int y = position.get(1).asInt();
        
        // And: Position should be within 2x2 world bounds (-1 to 1 for both x and y)
        assertTrue(x >= -1 && x <= 1, "X coordinate should be within [-1, 1]");
        assertTrue(y >= -1 && y <= 1, "Y coordinate should be within [-1, 1]");
    }
}
