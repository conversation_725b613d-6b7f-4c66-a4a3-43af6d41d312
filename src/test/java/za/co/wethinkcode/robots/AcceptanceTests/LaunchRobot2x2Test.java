package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Launch Robot Tests for Different World Configurations
 *
 * Test Organization by World Configuration:
 * - 1x1 Empty World: 1 robot capacity
 * - 1x1 World with Obstacle: 0 robot capacity
 * - 2x2 Empty World: 9 robot capacity
 * - 2x2 World with Obstacle: 8 robot capacity
 *
 * Server Configuration Instructions:
 * - 1x1 Empty: java -jar server.jar -s 1
 * - 1x1 with Obstacle: java -jar server.jar -s 1 -o 0,0
 * - 2x2 Empty: java -jar server.jar -s 2
 * - 2x2 with Obstacle: java -jar server.jar -s 2 -o 1,1
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class LaunchRobot2x2Test {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    // ========== 1x1 EMPTY WORLD TESTS ==========
    // Server: java -jar server.jar -s 1

    @Test
    @Order(1)
    @DisplayName("1x1 Empty World: Valid launch should succeed at (0,0)")
    void launch1x1EmptyWorldSuccess() {
        // Given: Connected to server with 1x1 empty world
        assertTrue(serverClient.isConnected());

        // When: Launch a robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Launch should succeed at position (0,0)
        assertEquals("OK", response.get("result").asText());
        assertEquals(0, response.get("data").get("position").get(0).asInt());
        assertEquals(0, response.get("data").get("position").get(1).asInt());
        assertNotNull(response.get("state"));
    }

    @Test
    @Order(2)
    @DisplayName("1x1 Empty World: Second robot should fail (world full)")
    void launch1x1EmptyWorldFull() {
        // Given: Connected to server with 1x1 empty world
        assertTrue(serverClient.isConnected());

        // And: First robot already launched
        String firstRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(firstRequest);

        // When: Try to launch second robot
        String secondRequest = "{" +
                "\"robot\": \"HAL2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(secondRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().contains("No more space") ||
                  response.get("data").get("message").asText().contains("World is full"));
    }

    // ========== 1x1 WORLD WITH OBSTACLE TESTS ==========
    // Server: java -jar server.jar -s 1 -o 0,0

    @Test
    @Order(3)
    @DisplayName("1x1 World with obstacle at (0,0): No robots should be able to launch")
    void launch1x1WorldWithObstacle() {
        // Given: Connected to server with 1x1 world with obstacle at (0,0)
        assertTrue(serverClient.isConnected());

        // When: Try to launch a robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Should fail because only position is blocked by obstacle
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                    response.get("data").get("message").asText());
    }

    // ========== 2x2 EMPTY WORLD TESTS ==========
    // Server: java -jar server.jar -s 2

    @Test
    @Order(4)
    @DisplayName("2x2 Empty World: Multiple robots should launch successfully")
    void launch2x2EmptyWorldMultipleRobots() {
        // Given: Connected to server with 2x2 empty world (9 positions)
        assertTrue(serverClient.isConnected());

        // When: Launch multiple robots
        String launchHAL = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseHAL = serverClient.sendRequest(launchHAL);

        String launchR2D2 = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseR2D2 = serverClient.sendRequest(launchR2D2);

        // Then: Both should succeed
        assertEquals("OK", responseHAL.get("result").asText());
        assertEquals("OK", responseR2D2.get("result").asText());

        // And: Should have valid positions
        assertNotNull(responseHAL.get("data").get("position"));
        assertNotNull(responseR2D2.get("data").get("position"));
        assertTrue(responseR2D2.get("data").get("position").isArray());
        assertEquals(2, responseR2D2.get("data").get("position").size());
    }

    @Test
    @Order(5)
    @DisplayName("2x2 Empty World: Should be full after 9 robots")
    void launch2x2EmptyWorldFull() {
        // Given: Connected to server with 2x2 empty world (9 positions)
        assertTrue(serverClient.isConnected());

        // And: 9 robots successfully launched (filling all positions)
        for (int i = 1; i <= 9; i++) {
            String request = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            assertEquals("OK", response.get("result").asText(),
                        "Robot" + i + " should launch successfully");
        }

        // When: Try to launch 10th robot
        String extraRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                    response.get("data").get("message").asText());
    }

    // ========== 2x2 WORLD WITH OBSTACLE TESTS ==========
    // Server: java -jar server.jar -s 2 -o 1,1

    @Test
    @Order(6)
    @DisplayName("2x2 World with obstacle at (1,1): Robots should not spawn at obstacle position")
    void launch2x2WorldWithObstacleRobotsAvoidObstacle() {
        // Given: Connected to server with 2x2 world with obstacle at (1,1)
        assertTrue(serverClient.isConnected());

        // When: Launch 8 robots (all available positions except obstacle)
        for (int i = 0; i < 8; i++) {
            String request = "{" +
                    "\"robot\": \"RoboCop" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);

            // Then: Each robot should launch successfully
            assertEquals("OK", response.get("result").asText(),
                        "RoboCop" + i + " should launch successfully");

            // And: No robot should be at obstacle position [1,1]
            JsonNode position = response.get("data").get("position");
            assertFalse(position.get(0).asInt() == 1 && position.get(1).asInt() == 1,
                       "RoboCop" + i + " should not be placed at obstacle position [1,1]");
        }
    }

    @Test
    @Order(7)
    @DisplayName("2x2 World with obstacle at (1,1): Should be full after 8 robots")
    void launch2x2WorldWithObstacleFull() {
        // Given: Connected to server with 2x2 world with obstacle at (1,1)
        // Available positions: 9 total - 1 obstacle = 8 positions
        assertTrue(serverClient.isConnected());

        // And: 8 robots successfully launched (filling all available positions)
        for (int i = 0; i < 8; i++) {
            String request = "{" +
                    "\"robot\": \"Terminator" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            assertEquals("OK", response.get("result").asText(),
                        "Terminator" + i + " should launch successfully");
        }

        // When: Try to launch 9th robot
        String extraRequest = "{" +
                "\"robot\": \"T1000\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                    response.get("data").get("message").asText());
    }
}