package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for look command in empty worlds (no obstacles)
 * 
 * Test Organization:
 * - Empty World: Look command should return empty results
 * - World with Other Robots: Look command should detect other robots
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class LookRobotEmptyWorldTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    // ========== EMPTY WORLD LOOK TESTS ==========
    
    @Test
    @Order(1)
    @DisplayName("Empty World: Look should return empty results")
    void lookInEmptyWorld() {
        // Given: Connected to server with empty world
        // Server should be started with: java -jar server.jar (default empty world)
        assertTrue(serverClient.isConnected());

        // And: Robot is launched
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
        assertEquals("OK", launchResponse.get("result").asText());

        // When: Robot looks around
        String lookRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(lookRequest);

        // Then: Should succeed
        assertEquals("OK", response.get("result").asText());
        
        // And: Should return empty objects and robots arrays
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("objects"));
        assertNotNull(response.get("data").get("robots"));
        assertTrue(response.get("data").get("objects").isArray());
        assertTrue(response.get("data").get("robots").isArray());
        assertEquals(0, response.get("data").get("objects").size());
        assertEquals(0, response.get("data").get("robots").size());
    }

    @Test
    @Order(2)
    @DisplayName("World with other robots: Look should detect nearby robots")
    void lookWithOtherRobots() {
        // Given: Connected to server with larger world
        assertTrue(serverClient.isConnected());

        // And: First robot is launched
        String launchHAL = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode halResponse = serverClient.sendRequest(launchHAL);
        assertEquals("OK", halResponse.get("result").asText());

        // And: Second robot is launched
        String launchR2D2 = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode r2d2Response = serverClient.sendRequest(launchR2D2);
        assertEquals("OK", r2d2Response.get("result").asText());

        // When: First robot looks around
        String lookRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(lookRequest);

        // Then: Should succeed
        assertEquals("OK", response.get("result").asText());
        
        // And: Should return data with objects and robots arrays
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("objects"));
        assertNotNull(response.get("data").get("robots"));
        assertTrue(response.get("data").get("objects").isArray());
        assertTrue(response.get("data").get("robots").isArray());
        
        // Note: The exact content depends on visibility range and robot positions
        // This test verifies the structure is correct
    }

    @Test
    @Order(3)
    @DisplayName("Look command: Should return robot state")
    void lookShouldReturnRobotState() {
        // Given: Connected to server
        assertTrue(serverClient.isConnected());

        // And: Robot is launched
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(launchRequest);

        // When: Robot looks around
        String lookRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(lookRequest);

        // Then: Should succeed and include robot state
        assertEquals("OK", response.get("result").asText());
        assertNotNull(response.get("state"));
        assertNotNull(response.get("state").get("position"));
        assertNotNull(response.get("state").get("direction"));
        assertNotNull(response.get("state").get("shields"));
        assertNotNull(response.get("state").get("shots"));
        assertNotNull(response.get("state").get("status"));
    }

    @Test
    @Order(4)
    @DisplayName("Look command: Invalid robot should fail")
    void lookWithInvalidRobot() {
        // Given: Connected to server
        assertTrue(serverClient.isConnected());

        // When: Try to look with non-existent robot
        String lookRequest = "{" +
                "\"robot\": \"NonExistentRobot\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(lookRequest);

        // Then: Should fail
        assertEquals("ERROR", response.get("result").asText());
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertTrue(response.get("data").get("message").asText().contains("Does not exist") ||
                  response.get("data").get("message").asText().contains("not found"));
    }

    @Test
    @Order(5)
    @DisplayName("Look command: Should work after robot movement")
    void lookAfterMovement() {
        // Given: Connected to server with larger world
        assertTrue(serverClient.isConnected());

        // And: Robot is launched
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(launchRequest);

        // And: Robot moves (if possible in current world)
        String moveRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"1\"]" +
                "}";
        serverClient.sendRequest(moveRequest); // May succeed or fail depending on world size

        // When: Robot looks around
        String lookRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(lookRequest);

        // Then: Look should still work regardless of move result
        assertEquals("OK", response.get("result").asText());
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("objects"));
        assertNotNull(response.get("data").get("robots"));
        assertNotNull(response.get("state"));
    }
}
