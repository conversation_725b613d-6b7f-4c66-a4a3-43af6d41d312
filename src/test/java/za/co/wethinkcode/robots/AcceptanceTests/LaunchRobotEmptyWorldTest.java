package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for launching robots in empty worlds (no obstacles)
 * 
 * Test Organization:
 * - 1x1 Empty World: Basic launch tests, capacity limits
 * - 2x2 Empty World: Multiple robot launches, full world scenarios
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class LaunchRobotEmptyWorldTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    // ========== 1x1 EMPTY WORLD TESTS ==========
    
    @Test
    @Order(1)
    @DisplayName("1x1 Empty World: Valid launch should succeed at (0,0)")
    void launch1x1EmptyWorldSuccess() {
        // Given: Connected to server with 1x1 empty world
        // Server should be started with: java -jar server.jar -s 1
        assertTrue(serverClient.isConnected());

        // When: Launch a robot
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Launch should succeed at position (0,0)
        assertEquals("OK", response.get("result").asText());
        assertEquals(0, response.get("data").get("position").get(0).asInt());
        assertEquals(0, response.get("data").get("position").get(1).asInt());
        assertNotNull(response.get("state"));
    }

    @Test
    @Order(2)
    @DisplayName("1x1 Empty World: Second robot should fail (world full)")
    void launch1x1EmptyWorldFull() {
        // Given: Connected to server with 1x1 empty world
        assertTrue(serverClient.isConnected());

        // And: First robot already launched
        String firstRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(firstRequest);

        // When: Try to launch second robot
        String secondRequest = "{" +
                "\"robot\": \"HAL2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(secondRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().contains("No more space") ||
                  response.get("data").get("message").asText().contains("World is full"));
    }

    @Test
    @Order(3)
    @DisplayName("1x1 Empty World: Duplicate robot name should fail")
    void launch1x1EmptyWorldDuplicateName() {
        // Given: Connected to server with 1x1 empty world
        assertTrue(serverClient.isConnected());

        // And: Robot "HAL" already exists
        String firstRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(firstRequest);

        // When: Try to launch another robot with same name
        String duplicateRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(duplicateRequest);

        // Then: Should fail with duplicate name error
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().contains("Too many of you in this world") ||
                  response.get("data").get("message").asText().contains("Duplicate robot"));
    }

    // ========== 2x2 EMPTY WORLD TESTS ==========

    @Test
    @Order(4)
    @DisplayName("2x2 Empty World: Multiple robots should launch successfully")
    void launch2x2EmptyWorldMultipleRobots() {
        // Given: Connected to server with 2x2 empty world (9 positions)
        // Server should be started with: java -jar server.jar -s 2
        assertTrue(serverClient.isConnected());

        // When: Launch multiple robots
        String firstRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode firstResponse = serverClient.sendRequest(firstRequest);

        String secondRequest = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode secondResponse = serverClient.sendRequest(secondRequest);

        // Then: Both should succeed
        assertEquals("OK", firstResponse.get("result").asText());
        assertEquals("OK", secondResponse.get("result").asText());
        
        // And: Should have valid positions
        assertNotNull(firstResponse.get("data").get("position"));
        assertNotNull(secondResponse.get("data").get("position"));
    }

    @Test
    @Order(5)
    @DisplayName("2x2 Empty World: Should be full after 9 robots")
    void launch2x2EmptyWorldFull() {
        // Given: Connected to server with 2x2 empty world (9 positions)
        assertTrue(serverClient.isConnected());

        // And: 9 robots successfully launched (filling all positions)
        for (int i = 1; i <= 9; i++) {
            String request = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            assertEquals("OK", response.get("result").asText(), 
                        "Robot" + i + " should launch successfully");
        }

        // When: Try to launch 10th robot
        String extraRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRequest);

        // Then: Should fail with world full error
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world", 
                    response.get("data").get("message").asText());
    }

    @Test
    @Order(6)
    @DisplayName("Invalid command should fail")
    void launchInvalidCommand() {
        // Given: Connected to server
        assertTrue(serverClient.isConnected());

        // When: Send invalid command "luanch" instead of "launch"
        String request = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"luanch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then: Should fail with unsupported command error
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().contains("Unsupported command"));
    }
}
