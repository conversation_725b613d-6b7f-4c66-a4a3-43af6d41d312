package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

/**
 * Command to reset the world state by clearing all robots and obstacles.
 * This is primarily used for testing purposes to ensure clean state between tests.
 */
public class ResetCommand extends Command {

    public ResetCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "reset";
    }

    @Override
    public Response execute(World world) {
        world.resetWorld();
        return new Response("OK", "World reset successfully");
    }
}
